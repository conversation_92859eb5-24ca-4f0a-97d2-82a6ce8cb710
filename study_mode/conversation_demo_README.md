# 对话Demo使用说明

这是一个基于Gradio的对话演示界面，使用自定义的`conversation`函数处理对话逻辑。

## 功能特性

- 🤖 **智能对话**: 基于自定义conversation函数的对话处理
- 💬 **实时聊天**: 支持实时对话交互
- 📝 **对话历史**: 自动保存和显示对话历史
- 🔧 **参数调节**: 支持温度参数和最大token数调节
- 📊 **对话统计**: 实时显示对话轮数统计
- 📤 **导出功能**: 支持导出完整对话历史
- 🎨 **美观界面**: 使用Gradio Soft主题，界面简洁美观

## 安装依赖

```bash
pip install -r conversation_demo_requirements.txt
```

或者直接安装gradio：

```bash
pip install gradio>=4.0.0
```

## 使用方法

### 1. 启动应用

```bash
python conversation_demo.py
```

### 2. 访问界面

启动后，在浏览器中访问：
```
http://localhost:8085
```

### 3. 自定义conversation函数

在`conversation_demo.py`文件中找到`conversation`函数，替换为你自己的对话逻辑：

```python
def conversation(message, history):
    """
    对话函数 - 这里是你需要实现的核心对话逻辑
    
    Args:
        message (str): 用户输入的消息
        history (list): 对话历史，格式为 [[user_msg, bot_msg], ...]
    
    Returns:
        str: 机器人的回复
    """
    # 在这里实现你的对话逻辑
    # 例如：调用你的AI模型、API等
    
    return "你的回复内容"
```

## 界面说明

### 主要组件

1. **对话窗口**: 显示用户和AI的对话内容
2. **输入框**: 用户输入消息的地方
3. **发送按钮**: 发送消息
4. **清空对话**: 清空当前对话历史
5. **导出对话**: 导出对话历史为文本格式

### 侧边栏设置

- **温度参数**: 控制回复的随机性（0.1-2.0）
- **最大token数**: 控制回复的最大长度（50-2000）
- **对话统计**: 显示当前对话轮数

## 自定义配置

### 修改端口

在文件末尾的`demo.launch()`中修改`server_port`参数：

```python
demo.launch(
    server_port=8085,  # 修改为你想要的端口
    server_name="0.0.0.0",
    share=False,
    debug=True,
    show_error=True
)
```

### 修改主题

在创建Gradio界面时修改主题：

```python
with gr.Blocks(theme=gr.themes.Soft(), title="AI对话助手") as demo:
    # 可选主题: gr.themes.Default(), gr.themes.Glass(), gr.themes.Monochrome()
```

### 添加更多功能

你可以在侧边栏添加更多配置选项，例如：

- 模型选择下拉框
- 系统提示词输入框
- 对话模式选择
- 其他自定义参数

## 集成你的对话模型

### 示例1: 集成OpenAI API

```python
import openai

def conversation(message, history):
    # 构建对话历史
    messages = [{"role": "system", "content": "你是一个有用的AI助手"}]
    for user_msg, bot_msg in history:
        messages.append({"role": "user", "content": user_msg})
        messages.append({"role": "assistant", "content": bot_msg})
    messages.append({"role": "user", "content": message})
    
    # 调用OpenAI API
    response = openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=messages
    )
    
    return response.choices[0].message.content
```

### 示例2: 集成本地模型

```python
from your_model import YourModel

model = YourModel()

def conversation(message, history):
    # 使用你的本地模型
    response = model.generate(message, history)
    return response
```

## 注意事项

1. 确保你的conversation函数能够正确处理输入参数
2. 对话历史格式为`[[user_msg, bot_msg], ...]`
3. 函数应该返回字符串类型的回复
4. 建议添加异常处理，确保程序稳定运行

## 故障排除

### 常见问题

1. **端口被占用**: 修改`server_port`为其他可用端口
2. **gradio版本问题**: 确保使用gradio 4.0.0或更高版本
3. **对话函数报错**: 检查conversation函数的实现和返回值

### 调试模式

启动时已开启调试模式，可以在控制台查看详细错误信息。

## 扩展建议

- 添加对话保存到文件的功能
- 集成语音输入/输出
- 添加多轮对话上下文管理
- 实现对话分支和回滚功能
- 添加用户身份验证
- 集成数据库存储对话历史
