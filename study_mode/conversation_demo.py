import gradio as gr
import time


def conversation(message, history):
    """
    对话函数 - 这里是你需要实现的核心对话逻辑
    
    Args:
        message (str): 用户输入的消息
        history (list): 对话历史，格式为 [[user_msg, bot_msg], ...]
    
    Returns:
        str: 机器人的回复
    """
    # 这里是示例实现，你可以替换为你自己的对话逻辑
    
    # 简单的示例回复逻辑
    if "你好" in message or "hello" in message.lower():
        return "你好！我是AI助手，有什么可以帮助你的吗？"
    elif "天气" in message:
        return "抱歉，我暂时无法获取实时天气信息，建议你查看天气应用或网站。"
    elif "时间" in message:
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        return f"当前时间是：{current_time}"
    elif "再见" in message or "拜拜" in message:
        return "再见！祝你有美好的一天！"
    else:
        # 这里可以接入你的实际对话模型
        return f"我收到了你的消息：'{message}'。这是一个示例回复，请替换为你的实际对话逻辑。"


def chat_interface(message, history):
    """
    Gradio聊天界面的处理函数
    
    Args:
        message (str): 用户输入的消息
        history (list): 对话历史
    
    Returns:
        tuple: (空字符串, 更新后的历史)
    """
    # 调用conversation函数获取回复
    bot_response = conversation(message, history)
    
    # 将新的对话添加到历史中
    history.append([message, bot_response])
    
    # 返回空字符串（清空输入框）和更新后的历史
    return "", history


def clear_chat():
    """清空对话历史"""
    return []


def export_chat(history):
    """导出对话历史"""
    if not history:
        return "没有对话历史可以导出。"
    
    export_text = "对话历史导出\n" + "="*50 + "\n\n"
    for i, (user_msg, bot_msg) in enumerate(history, 1):
        export_text += f"对话 {i}:\n"
        export_text += f"用户: {user_msg}\n"
        export_text += f"助手: {bot_msg}\n"
        export_text += "-" * 30 + "\n\n"
    
    return export_text


# 创建Gradio界面
with gr.Blocks(theme=gr.themes.Soft(), title="AI对话助手") as demo:
    gr.Markdown("# 🤖 AI对话助手")
    gr.Markdown("这是一个基于Gradio的对话演示界面，使用自定义的conversation函数处理对话逻辑。")
    
    with gr.Row():
        with gr.Column(scale=4):
            # 聊天界面
            chatbot = gr.Chatbot(
                label="对话窗口",
                height=500,
                show_label=True,
                container=True,
                bubble_full_width=False
            )
            
            with gr.Row():
                msg_input = gr.Textbox(
                    label="输入消息",
                    placeholder="请输入你的消息...",
                    lines=2,
                    scale=4
                )
                send_btn = gr.Button("发送", variant="primary", scale=1)
            
            with gr.Row():
                clear_btn = gr.Button("清空对话", variant="secondary")
                export_btn = gr.Button("导出对话", variant="secondary")
        
        with gr.Column(scale=1):
            # 侧边栏 - 可以添加一些配置选项
            gr.Markdown("### 设置")
            
            # 可以添加一些对话参数设置
            temperature = gr.Slider(
                minimum=0.1,
                maximum=2.0,
                value=0.7,
                step=0.1,
                label="温度参数",
                info="控制回复的随机性"
            )
            
            max_tokens = gr.Slider(
                minimum=50,
                maximum=2000,
                value=500,
                step=50,
                label="最大token数",
                info="控制回复的最大长度"
            )
            
            # 显示对话统计
            gr.Markdown("### 对话统计")
            chat_count = gr.Number(
                label="对话轮数",
                value=0,
                interactive=False
            )
    
    # 导出结果显示区域
    export_output = gr.Textbox(
        label="导出的对话历史",
        lines=10,
        visible=False
    )
    
    # 事件绑定
    def update_chat_count(history):
        return len(history)
    
    def send_message(message, history):
        if message.strip():
            new_msg, new_history = chat_interface(message, history)
            return new_msg, new_history, len(new_history)
        return message, history, len(history)
    
    def show_export(history):
        export_text = export_chat(history)
        return {
            export_output: gr.update(value=export_text, visible=True)
        }
    
    def clear_and_hide_export(history):
        return [], 0, gr.update(visible=False)
    
    # 绑定发送按钮
    send_btn.click(
        fn=send_message,
        inputs=[msg_input, chatbot],
        outputs=[msg_input, chatbot, chat_count]
    )
    
    # 绑定回车键发送
    msg_input.submit(
        fn=send_message,
        inputs=[msg_input, chatbot],
        outputs=[msg_input, chatbot, chat_count]
    )
    
    # 绑定清空按钮
    clear_btn.click(
        fn=clear_and_hide_export,
        inputs=[chatbot],
        outputs=[chatbot, chat_count, export_output]
    )
    
    # 绑定导出按钮
    export_btn.click(
        fn=show_export,
        inputs=[chatbot],
        outputs=[export_output]
    )


if __name__ == "__main__":
    # 启动应用
    demo.launch()
